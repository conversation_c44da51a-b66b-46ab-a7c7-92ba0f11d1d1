use crate::core::{
    serial_manager::SerialManager,
    types::{SerialPortInfo, SmsMessage, SmsType},
};
use crate::gui::app_state::AppState;
use log::{debug, error, info};
use serde::{Deserialize, Serialize};
use tauri::State;

#[derive(Debug, Serialize, Deserialize)]
pub struct SendSmsRequest {
    pub phone_number: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SmsHistoryResponse {
    pub messages: Vec<SmsMessage>,
    pub total_count: u32,
}

/// Get available serial ports
#[tauri::command]
pub async fn get_serial_ports() -> Result<Vec<SerialPortInfo>, String> {
    debug!("Getting available serial ports");
    
    match SerialManager::get_available_ports() {
        Ok(ports) => {
            info!("Found {} serial ports", ports.len());
            Ok(ports)
        }
        Err(e) => {
            error!("Failed to get serial ports: {}", e);
            Err(format!("Failed to get serial ports: {}", e))
        }
    }
}

/// Connect to serial port
#[tauri::command]
pub async fn connect_serial(
    port_name: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    info!("Connecting to serial port: {}", port_name);
    
    let mut serial_manager = state.serial_manager.lock().await;
    let mut sms_manager = state.sms_manager.lock().await;
    
    match serial_manager.connect(&port_name) {
        Ok(_) => {
            // Test connection
            match serial_manager.test_connection() {
                Ok(true) => {
                    // Initialize SMS manager
                    match sms_manager.initialize(&mut *serial_manager) {
                        Ok(_) => {
                            info!("Successfully connected and initialized");
                            Ok("Connected successfully".to_string())
                        }
                        Err(e) => {
                            error!("Failed to initialize SMS manager: {}", e);
                            let _ = serial_manager.disconnect();
                            Err(format!("Failed to initialize SMS: {}", e))
                        }
                    }
                }
                Ok(false) => {
                    error!("AT command test failed");
                    let _ = serial_manager.disconnect();
                    Err("Device not responding to AT commands".to_string())
                }
                Err(e) => {
                    error!("Connection test failed: {}", e);
                    let _ = serial_manager.disconnect();
                    Err(format!("Connection test failed: {}", e))
                }
            }
        }
        Err(e) => {
            error!("Failed to connect to {}: {}", port_name, e);
            Err(format!("Failed to connect: {}", e))
        }
    }
}

/// Disconnect from serial port
#[tauri::command]
pub async fn disconnect_serial(state: State<'_, AppState>) -> Result<String, String> {
    info!("Disconnecting from serial port");
    
    let mut serial_manager = state.serial_manager.lock().await;
    
    match serial_manager.disconnect() {
        Ok(_) => {
            info!("Disconnected successfully");
            Ok("Disconnected successfully".to_string())
        }
        Err(e) => {
            error!("Failed to disconnect: {}", e);
            Err(format!("Failed to disconnect: {}", e))
        }
    }
}

/// Send SMS message
#[tauri::command]
pub async fn send_sms(
    request: SendSmsRequest,
    state: State<'_, AppState>,
) -> Result<String, String> {
    info!("Sending SMS to {}: {}", request.phone_number, request.message);
    
    let mut serial_manager = state.serial_manager.lock().await;
    let sms_manager = state.sms_manager.lock().await;
    let mut database = state.database.lock().await;
    
    // Check if connected
    if !serial_manager.is_connected() {
        return Err("Not connected to serial port".to_string());
    }
    
    // Send SMS
    match sms_manager.send_sms(&mut *serial_manager, &request.phone_number, &request.message) {
        Ok(_) => {
            // Save to database
            let sms_message = SmsMessage {
                id: None,
                phone_no: "-".to_string(),
                phone_to: request.phone_number.clone(),
                message: request.message.clone(),
                create_date: chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string(),
                message_type: SmsType::Sent,
                batch_no: 0,
                order_no: 0,
            };
            
            match database.save_sms(&sms_message).await {
                Ok(id) => {
                    info!("SMS sent and saved with ID: {}", id);
                    Ok("SMS sent successfully".to_string())
                }
                Err(e) => {
                    error!("Failed to save sent SMS: {}", e);
                    // SMS was sent but not saved - still consider it a success
                    Ok("SMS sent successfully (not saved to history)".to_string())
                }
            }
        }
        Err(e) => {
            error!("Failed to send SMS: {}", e);
            Err(format!("Failed to send SMS: {}", e))
        }
    }
}

/// Get SMS history
#[tauri::command]
pub async fn get_sms_history(
    limit: Option<u32>,
    offset: Option<u32>,
    state: State<'_, AppState>,
) -> Result<SmsHistoryResponse, String> {
    let limit = limit.unwrap_or(100);
    let offset = offset.unwrap_or(0);
    
    debug!("Getting SMS history: limit={}, offset={}", limit, offset);
    
    let database = state.database.lock().await;
    
    match database.get_sms_history(limit, offset).await {
        Ok(messages) => {
            info!("Retrieved {} SMS messages", messages.len());
            Ok(SmsHistoryResponse {
                messages,
                total_count: limit, // TODO: Get actual total count
            })
        }
        Err(e) => {
            error!("Failed to get SMS history: {}", e);
            Err(format!("Failed to get SMS history: {}", e))
        }
    }
}

/// Delete SMS message
#[tauri::command]
pub async fn delete_sms(
    id: i64,
    state: State<'_, AppState>,
) -> Result<String, String> {
    info!("Deleting SMS with ID: {}", id);
    
    let mut database = state.database.lock().await;
    
    match database.delete_sms(id).await {
        Ok(true) => {
            info!("SMS deleted successfully");
            Ok("SMS deleted successfully".to_string())
        }
        Ok(false) => {
            error!("SMS not found with ID: {}", id);
            Err("SMS not found".to_string())
        }
        Err(e) => {
            error!("Failed to delete SMS: {}", e);
            Err(format!("Failed to delete SMS: {}", e))
        }
    }
}

/// Get connection status
#[tauri::command]
pub async fn get_connection_status(state: State<'_, AppState>) -> Result<bool, String> {
    let serial_manager = state.serial_manager.lock().await;
    Ok(serial_manager.is_connected())
}

/// Get signal strength
#[tauri::command]
pub async fn get_signal_strength(state: State<'_, AppState>) -> Result<i32, String> {
    let mut serial_manager = state.serial_manager.lock().await;
    
    if !serial_manager.is_connected() {
        return Err("Not connected".to_string());
    }
    
    match serial_manager.get_signal_strength() {
        Ok(strength) => Ok(strength),
        Err(e) => {
            error!("Failed to get signal strength: {}", e);
            Err(format!("Failed to get signal strength: {}", e))
        }
    }
}
