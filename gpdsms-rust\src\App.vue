<template>
  <div id="app">
    <el-container class="app-container">
      <!-- Header -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo">
            <el-icon><Phone /></el-icon>
            <span>GPDSMS-Rust</span>
          </div>
          <div class="header-actions">
            <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="badge">
              <el-button type="primary" @click="showSendDialog = true">
                <el-icon><Plus /></el-icon>
                新短信
              </el-button>
            </el-badge>
            <el-dropdown @command="handleMenuCommand">
              <el-button type="text">
                <el-icon><Setting /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="settings">设置</el-dropdown-item>
                  <el-dropdown-item command="about">关于</el-dropdown-item>
                  <el-dropdown-item divided command="exit">退出</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <!-- Main Content -->
      <el-container>
        <!-- Sidebar -->
        <el-aside width="300px" class="sidebar">
          <div class="connection-status">
            <el-card>
              <div class="status-content">
                <div class="status-indicator">
                  <el-icon :class="connectionStatus ? 'connected' : 'disconnected'">
                    <Connection />
                  </el-icon>
                  <span>{{ connectionStatus ? '已连接' : '未连接' }}</span>
                </div>
                <el-button 
                  size="small" 
                  :type="connectionStatus ? 'danger' : 'primary'"
                  @click="toggleConnection"
                  :loading="connecting"
                >
                  {{ connectionStatus ? '断开' : '连接' }}
                </el-button>
              </div>
            </el-card>
          </div>

          <div class="port-selection" v-if="!connectionStatus">
            <el-select 
              v-model="selectedPort" 
              placeholder="选择串口"
              @focus="refreshPorts"
              style="width: 100%"
            >
              <el-option
                v-for="port in availablePorts"
                :key="port.port_name"
                :label="`${port.port_name} ${port.description || ''}`"
                :value="port.port_name"
              />
            </el-select>
          </div>

          <div class="signal-strength" v-if="connectionStatus">
            <el-card>
              <div class="signal-content">
                <el-icon><Antenna /></el-icon>
                <span>信号强度: {{ signalStrength }}</span>
              </div>
            </el-card>
          </div>
        </el-aside>

        <!-- Main Content Area -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>

    <!-- Send SMS Dialog -->
    <SendSmsDialog 
      v-model:visible="showSendDialog"
      @sent="onSmsSent"
    />

    <!-- Settings Dialog -->
    <SettingsDialog 
      v-model:visible="showSettingsDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Phone, Plus, Setting, Connection, Antenna } from '@element-plus/icons-vue';
import { invoke } from '@tauri-apps/api/tauri';
import { appWindow } from '@tauri-apps/api/window';
import SendSmsDialog from './components/SendSmsDialog.vue';
import SettingsDialog from './components/SettingsDialog.vue';

// Reactive data
const connectionStatus = ref(false);
const connecting = ref(false);
const selectedPort = ref('');
const availablePorts = ref<any[]>([]);
const signalStrength = ref(0);
const unreadCount = ref(0);
const showSendDialog = ref(false);
const showSettingsDialog = ref(false);

// Methods
const refreshPorts = async () => {
  try {
    availablePorts.value = await invoke('get_serial_ports');
  } catch (error) {
    ElMessage.error('获取串口列表失败: ' + error);
  }
};

const toggleConnection = async () => {
  if (connectionStatus.value) {
    await disconnect();
  } else {
    await connect();
  }
};

const connect = async () => {
  if (!selectedPort.value) {
    ElMessage.warning('请选择串口');
    return;
  }

  connecting.value = true;
  try {
    await invoke('connect_serial', { portName: selectedPort.value });
    connectionStatus.value = true;
    ElMessage.success('连接成功');
    startSignalMonitoring();
  } catch (error) {
    ElMessage.error('连接失败: ' + error);
  } finally {
    connecting.value = false;
  }
};

const disconnect = async () => {
  connecting.value = true;
  try {
    await invoke('disconnect_serial');
    connectionStatus.value = false;
    signalStrength.value = 0;
    ElMessage.success('已断开连接');
  } catch (error) {
    ElMessage.error('断开连接失败: ' + error);
  } finally {
    connecting.value = false;
  }
};

const startSignalMonitoring = () => {
  const interval = setInterval(async () => {
    if (!connectionStatus.value) {
      clearInterval(interval);
      return;
    }
    
    try {
      signalStrength.value = await invoke('get_signal_strength');
    } catch (error) {
      console.error('获取信号强度失败:', error);
    }
  }, 5000);
};

const handleMenuCommand = (command: string) => {
  switch (command) {
    case 'settings':
      showSettingsDialog.value = true;
      break;
    case 'about':
      ElMessageBox.alert('GPDSMS-Rust v1.5.0\n基于Rust和Tauri开发的短信管理工具', '关于', {
        confirmButtonText: '确定'
      });
      break;
    case 'exit':
      appWindow.close();
      break;
  }
};

const onSmsSent = () => {
  ElMessage.success('短信发送成功');
  // Refresh message list if needed
};

// Lifecycle
onMounted(async () => {
  await refreshPorts();
  
  // Check initial connection status
  try {
    connectionStatus.value = await invoke('get_connection_status');
  } catch (error) {
    console.error('获取连接状态失败:', error);
  }
});

onUnmounted(() => {
  // Cleanup if needed
});
</script>

<style scoped>
.app-container {
  height: 100vh;
}

.app-header {
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sidebar {
  background: #f5f7fa;
  padding: 20px;
  border-right: 1px solid #e4e7ed;
}

.connection-status {
  margin-bottom: 20px;
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connected {
  color: #67c23a;
}

.disconnected {
  color: #f56c6c;
}

.port-selection {
  margin-bottom: 20px;
}

.signal-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.main-content {
  padding: 20px;
}

.badge {
  margin-right: 10px;
}
</style>
