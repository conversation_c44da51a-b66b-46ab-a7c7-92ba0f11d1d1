[package]
name = "gpdsms-rust"
version = "1.5.0"
edition = "2021"
description = "SMS management application for Quectel 4G modules on PC"
authors = ["Your Name <<EMAIL>>"]
license = "MIT"

[dependencies]
# GUI Framework - Using Tauri for cross-platform desktop app
tauri = { version = "1.5", features = ["api-all", "system-tray", "shell-open"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Serial port communication
serialport = "4.3"
tokio = { version = "1.0", features = ["full"] }
tokio-serial = "5.4"

# Database
rusqlite = { version = "0.30", features = ["bundled"] }

# System integration
notify-rust = "4.8"

# Windows specific
[target.'cfg(windows)'.dependencies]
winreg = "0.50"
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_Registry",
    "Win32_UI_Shell",
    "Win32_UI_WindowsAndMessaging"
]}

# Encoding and text processing
encoding_rs = "0.8"
regex = "1.10"

# Logging
log = "0.4"
env_logger = "0.10"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Async utilities
futures = "0.3"

# Date and time
chrono = { version = "0.4", features = ["serde"] }

# Directory utilities
dirs = "5.0"

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[[bin]]
name = "gpdsms-rust"
path = "src/main.rs"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]
