<template>
  <el-dialog
    v-model="dialogVisible"
    title="设置"
    width="600px"
    @close="handleClose"
  >
    <el-tabs v-model="activeTab">
      <!-- General Settings -->
      <el-tab-pane label="常规设置" name="general">
        <el-form label-width="120px">
          <el-form-item label="开机自启动">
            <el-switch
              v-model="settings.autoStart"
              @change="handleAutoStartChange"
            />
            <div class="setting-description">
              启用后，应用程序将在系统启动时自动运行
            </div>
          </el-form-item>
          
          <el-form-item label="最小化到托盘">
            <el-switch v-model="settings.minimizeToTray" />
            <div class="setting-description">
              关闭窗口时最小化到系统托盘而不是退出程序
            </div>
          </el-form-item>
          
          <el-form-item label="新消息通知">
            <el-switch v-model="settings.showNotifications" />
            <div class="setting-description">
              收到新短信时显示桌面通知
            </div>
          </el-form-item>
          
          <el-form-item label="通知声音">
            <el-switch 
              v-model="settings.notificationSound"
              :disabled="!settings.showNotifications"
            />
            <div class="setting-description">
              收到新短信时播放提示音
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- Connection Settings -->
      <el-tab-pane label="连接设置" name="connection">
        <el-form label-width="120px">
          <el-form-item label="自动重连">
            <el-switch v-model="settings.autoReconnect" />
            <div class="setting-description">
              连接断开时自动尝试重新连接
            </div>
          </el-form-item>
          
          <el-form-item label="重连间隔">
            <el-input-number
              v-model="settings.reconnectInterval"
              :min="5"
              :max="300"
              :step="5"
              :disabled="!settings.autoReconnect"
            />
            <span style="margin-left: 10px;">秒</span>
            <div class="setting-description">
              自动重连的时间间隔
            </div>
          </el-form-item>
          
          <el-form-item label="连接超时">
            <el-input-number
              v-model="settings.connectionTimeout"
              :min="5"
              :max="60"
              :step="1"
            />
            <span style="margin-left: 10px;">秒</span>
            <div class="setting-description">
              串口连接的超时时间
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- Database Settings -->
      <el-tab-pane label="数据管理" name="database">
        <el-form label-width="120px">
          <el-form-item label="自动清理">
            <el-switch v-model="settings.autoCleanup" />
            <div class="setting-description">
              自动清理超过指定天数的短信记录
            </div>
          </el-form-item>
          
          <el-form-item label="保留天数">
            <el-input-number
              v-model="settings.retentionDays"
              :min="7"
              :max="365"
              :step="1"
              :disabled="!settings.autoCleanup"
            />
            <span style="margin-left: 10px;">天</span>
            <div class="setting-description">
              短信记录的保留天数
            </div>
          </el-form-item>
          
          <el-form-item label="数据库操作">
            <div class="database-actions">
              <el-button @click="exportDatabase">导出数据</el-button>
              <el-button @click="importDatabase">导入数据</el-button>
              <el-button type="warning" @click="cleanupDatabase">清理数据库</el-button>
              <el-button type="danger" @click="clearAllData">清空所有数据</el-button>
            </div>
            <div class="setting-description">
              数据库维护和备份操作
            </div>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- About -->
      <el-tab-pane label="关于" name="about">
        <div class="about-content">
          <div class="app-info">
            <h3>GPDSMS-Rust</h3>
            <p>版本: 1.5.0</p>
            <p>基于 Rust 和 Tauri 开发的短信管理工具</p>
          </div>
          
          <div class="system-info">
            <h4>系统信息</h4>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="操作系统">{{ systemInfo.os }}</el-descriptions-item>
              <el-descriptions-item label="架构">{{ systemInfo.arch }}</el-descriptions-item>
              <el-descriptions-item label="Tauri版本">1.5.0</el-descriptions-item>
              <el-descriptions-item label="Rust版本">1.70+</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="links">
            <el-button type="primary" @click="openGithub">GitHub 仓库</el-button>
            <el-button @click="checkUpdates">检查更新</el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { shell } from '@tauri-apps/api';

// Props
interface Props {
  visible: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// Reactive data
const dialogVisible = ref(false);
const activeTab = ref('general');

const settings = ref({
  autoStart: false,
  minimizeToTray: true,
  showNotifications: true,
  notificationSound: true,
  autoReconnect: true,
  reconnectInterval: 30,
  connectionTimeout: 10,
  autoCleanup: false,
  retentionDays: 90
});

const systemInfo = ref({
  os: 'Unknown',
  arch: 'Unknown'
});

// Watch for prop changes
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    loadSettings();
  }
});

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
});

// Methods
const loadSettings = () => {
  // Load settings from local storage or backend
  const savedSettings = localStorage.getItem('gpdsms-settings');
  if (savedSettings) {
    try {
      Object.assign(settings.value, JSON.parse(savedSettings));
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }
};

const saveSettings = () => {
  try {
    localStorage.setItem('gpdsms-settings', JSON.stringify(settings.value));
    ElMessage.success('设置已保存');
    handleClose();
  } catch (error) {
    ElMessage.error('保存设置失败: ' + error);
  }
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleAutoStartChange = async (value: boolean) => {
  try {
    // This would call a Tauri command to manage startup
    // await invoke('manage_startup', { enable: value });
    ElMessage.success(value ? '已启用开机自启动' : '已禁用开机自启动');
  } catch (error) {
    ElMessage.error('设置开机自启动失败: ' + error);
    settings.value.autoStart = !value; // Revert on error
  }
};

const exportDatabase = async () => {
  try {
    ElMessage.info('导出功能开发中...');
  } catch (error) {
    ElMessage.error('导出失败: ' + error);
  }
};

const importDatabase = async () => {
  try {
    ElMessage.info('导入功能开发中...');
  } catch (error) {
    ElMessage.error('导入失败: ' + error);
  }
};

const cleanupDatabase = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理数据库吗？这将删除过期的短信记录。',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    ElMessage.info('清理功能开发中...');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清理失败: ' + error);
    }
  }
};

const clearAllData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有数据吗？此操作不可恢复！',
      '危险操作',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error',
      }
    );
    
    ElMessage.info('清空功能开发中...');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败: ' + error);
    }
  }
};

const openGithub = async () => {
  try {
    await shell.open('https://github.com/your-username/gpdsms-rust');
  } catch (error) {
    ElMessage.error('无法打开链接: ' + error);
  }
};

const checkUpdates = () => {
  ElMessage.info('检查更新功能开发中...');
};

// Lifecycle
onMounted(() => {
  // Get system info
  systemInfo.value = {
    os: navigator.platform,
    arch: navigator.userAgent.includes('x64') ? 'x64' : 'x86'
  };
});
</script>

<style scoped>
.setting-description {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.database-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.about-content {
  padding: 20px 0;
}

.app-info {
  text-align: center;
  margin-bottom: 30px;
}

.app-info h3 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.app-info p {
  margin: 5px 0;
  color: #606266;
}

.system-info {
  margin-bottom: 30px;
}

.system-info h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.links {
  text-align: center;
}

.links .el-button {
  margin: 0 10px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
