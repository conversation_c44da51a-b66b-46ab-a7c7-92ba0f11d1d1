<template>
  <el-dialog
    v-model="dialogVisible"
    title="发送短信"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="form.phoneNumber"
          placeholder="请输入手机号码"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="短信内容" prop="message">
        <el-input
          v-model="form.message"
          type="textarea"
          :rows="6"
          placeholder="请输入短信内容"
          show-word-limit
          :maxlength="500"
        />
      </el-form-item>
      
      <el-form-item>
        <div class="message-info">
          <span>字符数: {{ form.message.length }}/500</span>
          <span>预计短信条数: {{ estimatedSmsCount }}</span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSend"
          :loading="sending"
        >
          发送
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { invoke } from '@tauri-apps/api/tauri';

// Props
interface Props {
  visible: boolean;
  phoneNumber?: string;
  message?: string;
}

const props = withDefaults(defineProps<Props>(), {
  phoneNumber: '',
  message: ''
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'sent': [];
}>();

// Reactive data
const dialogVisible = ref(false);
const sending = ref(false);
const formRef = ref<FormInstance>();

const form = ref({
  phoneNumber: '',
  message: ''
});

// Form validation rules
const rules: FormRules = {
  phoneNumber: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: '请输入正确的手机号码', 
      trigger: 'blur' 
    }
  ],
  message: [
    { required: true, message: '请输入短信内容', trigger: 'blur' },
    { min: 1, max: 500, message: '短信内容长度在 1 到 500 个字符', trigger: 'blur' }
  ]
};

// Computed
const estimatedSmsCount = computed(() => {
  const message = form.value.message;
  if (!message) return 0;
  
  // Simple estimation: 70 characters per SMS for ASCII, 35 for Unicode
  const hasUnicode = /[^\x00-\x7F]/.test(message);
  const maxCharsPerSms = hasUnicode ? 35 : 70;
  
  return Math.ceil(message.length / maxCharsPerSms);
});

// Watch for prop changes
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    // Reset form when dialog opens
    form.value.phoneNumber = props.phoneNumber || '';
    form.value.message = props.message || '';
  }
});

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
});

// Methods
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  form.value = {
    phoneNumber: '',
    message: ''
  };
  formRef.value?.clearValidate();
};

const handleSend = async () => {
  if (!formRef.value) return;
  
  try {
    const valid = await formRef.value.validate();
    if (!valid) return;
    
    sending.value = true;
    
    await invoke('send_sms', {
      request: {
        phone_number: form.value.phoneNumber,
        message: form.value.message
      }
    });
    
    ElMessage.success('短信发送成功');
    emit('sent');
    handleClose();
    
  } catch (error) {
    ElMessage.error('发送失败: ' + error);
  } finally {
    sending.value = false;
  }
};
</script>

<style scoped>
.message-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
