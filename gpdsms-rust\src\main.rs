// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod core;
mod gui;
mod utils;

use crate::core::{
    database::Database,
    serial_manager::SerialManager,
    sms_manager::SmsManager,
};
use crate::gui::app_state::AppState;
use std::sync::Arc;
use tauri::{Manager, SystemTray, SystemTrayEvent, SystemTrayMenu, CustomMenuItem};
use tokio::sync::Mutex;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    env_logger::init();
    
    // Initialize core components
    let database = Arc::new(Mutex::new(Database::new().await?));
    let serial_manager = Arc::new(Mutex::new(SerialManager::new()));
    let sms_manager = Arc::new(Mutex::new(SmsManager::new()));
    
    // Create app state
    let app_state = AppState {
        database: database.clone(),
        serial_manager: serial_manager.clone(),
        sms_manager: sms_manager.clone(),
    };

    // Create system tray
    let tray_menu = SystemTrayMenu::new()
        .add_item(CustomMenuItem::new("show", "显示主窗口"))
        .add_item(CustomMenuItem::new("new_sms", "新短信"))
        .add_item(CustomMenuItem::new("history", "查看历史"))
        .add_native_item(tauri::SystemTrayMenuItem::Separator)
        .add_item(CustomMenuItem::new("quit", "退出"));

    let system_tray = SystemTray::new()
        .with_menu(tray_menu)
        .with_tooltip("GPDSMS - SMS管理工具");

    tauri::Builder::default()
        .manage(app_state)
        .system_tray(system_tray)
        .on_system_tray_event(|app, event| match event {
            SystemTrayEvent::LeftClick { .. } => {
                let window = app.get_window("main").unwrap();
                window.show().unwrap();
                window.set_focus().unwrap();
            }
            SystemTrayEvent::MenuItemClick { id, .. } => {
                match id.as_str() {
                    "show" => {
                        let window = app.get_window("main").unwrap();
                        window.show().unwrap();
                        window.set_focus().unwrap();
                    }
                    "quit" => {
                        std::process::exit(0);
                    }
                    _ => {}
                }
            }
            _ => {}
        })
        .invoke_handler(tauri::generate_handler![
            gui::commands::get_serial_ports,
            gui::commands::connect_serial,
            gui::commands::disconnect_serial,
            gui::commands::send_sms,
            gui::commands::get_sms_history,
            gui::commands::delete_sms,
        ])
        .setup(|app| {
            // Hide window on startup (start minimized to tray)
            let window = app.get_window("main").unwrap();
            window.hide().unwrap();
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");

    Ok(())
}
