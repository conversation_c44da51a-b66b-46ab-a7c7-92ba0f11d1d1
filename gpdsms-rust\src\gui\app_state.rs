use crate::core::{
    database::Database,
    serial_manager::<PERSON><PERSON>Manager,
    sms_manager::SmsManager,
};
use std::sync::Arc;
use tokio::sync::Mutex;

/// Application state shared between GUI and backend
#[derive(Clone)]
pub struct AppState {
    pub database: Arc<Mutex<Database>>,
    pub serial_manager: Arc<Mutex<SerialManager>>,
    pub sms_manager: Arc<Mutex<SmsManager>>,
}

impl AppState {
    pub fn new(
        database: Arc<Mutex<Database>>,
        serial_manager: Arc<Mutex<SerialManager>>,
        sms_manager: Arc<Mutex<SmsManager>>,
    ) -> Self {
        Self {
            database,
            serial_manager,
            sms_manager,
        }
    }
}
