<template>
  <div class="message-list">
    <div class="list-header">
      <h2>短信历史</h2>
      <div class="header-actions">
        <el-input
          v-model="searchText"
          placeholder="搜索短信..."
          style="width: 200px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button @click="refreshMessages" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div class="message-content">
      <el-table 
        :data="filteredMessages" 
        v-loading="loading"
        @row-dblclick="handleRowDoubleClick"
        height="100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="联系人" width="150">
          <template #default="scope">
            {{ scope.row.message_type === 'received' ? scope.row.phone_no : scope.row.phone_to }}
          </template>
        </el-table-column>
        <el-table-column prop="message" label="内容" min-width="300">
          <template #default="scope">
            <div class="message-preview">
              {{ truncateMessage(scope.row.message) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="create_date" label="时间" width="180" />
        <el-table-column label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.message_type === 'received' ? 'success' : 'primary'">
              {{ scope.row.message_type === 'received' ? '接收' : '发送' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button 
              size="small" 
              @click="replyToMessage(scope.row)"
              :disabled="scope.row.message_type === 'sent'"
            >
              回复
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteMessage(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :total="totalMessages"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- Message Detail Dialog -->
    <el-dialog
      v-model="showDetailDialog"
      title="短信详情"
      width="600px"
    >
      <div v-if="selectedMessage" class="message-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="联系人">
            {{ selectedMessage.message_type === 'received' ? selectedMessage.phone_no : selectedMessage.phone_to }}
          </el-descriptions-item>
          <el-descriptions-item label="时间">
            {{ selectedMessage.create_date }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="selectedMessage.message_type === 'received' ? 'success' : 'primary'">
              {{ selectedMessage.message_type === 'received' ? '接收' : '发送' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="内容">
            <div class="message-content-detail">
              {{ selectedMessage.message }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="replyToMessage(selectedMessage)"
            v-if="selectedMessage?.message_type === 'received'"
          >
            回复
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import { invoke } from '@tauri-apps/api/tauri';

// Types
interface SmsMessage {
  id: number;
  phone_no: string;
  phone_to: string;
  message: string;
  create_date: string;
  message_type: 'received' | 'sent';
  batch_no: number;
  order_no: number;
}

// Reactive data
const messages = ref<SmsMessage[]>([]);
const loading = ref(false);
const searchText = ref('');
const currentPage = ref(1);
const pageSize = ref(50);
const totalMessages = ref(0);
const showDetailDialog = ref(false);
const selectedMessage = ref<SmsMessage | null>(null);

// Computed
const filteredMessages = computed(() => {
  if (!searchText.value) {
    return messages.value;
  }
  
  const search = searchText.value.toLowerCase();
  return messages.value.filter(msg => 
    msg.message.toLowerCase().includes(search) ||
    msg.phone_no.includes(search) ||
    msg.phone_to.includes(search)
  );
});

// Methods
const refreshMessages = async () => {
  loading.value = true;
  try {
    const offset = (currentPage.value - 1) * pageSize.value;
    const response = await invoke('get_sms_history', {
      limit: pageSize.value,
      offset: offset
    }) as { messages: SmsMessage[], total_count: number };
    
    messages.value = response.messages;
    totalMessages.value = response.total_count;
  } catch (error) {
    ElMessage.error('获取短信历史失败: ' + error);
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  // Search is handled by computed property
};

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1;
  refreshMessages();
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  refreshMessages();
};

const handleRowDoubleClick = (row: SmsMessage) => {
  selectedMessage.value = row;
  showDetailDialog.value = true;
};

const replyToMessage = (message: SmsMessage) => {
  // Emit event to parent to show send dialog with pre-filled phone number
  const phoneNumber = message.message_type === 'received' ? message.phone_no : message.phone_to;
  // This would typically emit an event or use a store to communicate with parent
  console.log('Reply to:', phoneNumber);
};

const deleteMessage = async (message: SmsMessage) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条短信吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    await invoke('delete_sms', { id: message.id });
    ElMessage.success('删除成功');
    refreshMessages();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error);
    }
  }
};

const truncateMessage = (message: string, maxLength: number = 50) => {
  if (message.length <= maxLength) {
    return message;
  }
  return message.substring(0, maxLength) + '...';
};

// Lifecycle
onMounted(() => {
  refreshMessages();
});
</script>

<style scoped>
.message-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.list-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.message-content {
  flex: 1;
  min-height: 0;
}

.message-preview {
  word-break: break-word;
  line-height: 1.4;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.message-detail {
  margin: 20px 0;
}

.message-content-detail {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>
