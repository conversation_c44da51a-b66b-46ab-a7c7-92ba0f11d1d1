{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "GPDSMS-Rust", "version": "1.5.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "notification": {"all": true}, "dialog": {"all": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": true, "copyFile": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.gpdsms.rust", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "GPDSMS-Rust", "width": 1000, "height": 700, "minWidth": 800, "minHeight": 600, "center": true, "visible": false}], "systemTray": {"iconPath": "icons/icon.ico", "iconAsTemplate": true, "menuOnLeftClick": false}}}