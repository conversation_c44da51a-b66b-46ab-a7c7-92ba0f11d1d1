{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "GPDSMS-Rust", "version": "1.5.0", "identifier": "com.gpdsms.rust", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "dist"}, "app": {"windows": [{"title": "GPDSMS-Rust", "width": 1000, "height": 700, "minWidth": 800, "minHeight": 600, "center": true, "visible": true, "resizable": true, "fullscreen": false}], "security": {"csp": null}, "trayIcon": {"iconPath": "icons/icon.ico", "iconAsTemplate": true, "menuOnLeftClick": false}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"shell": {"open": true}, "dialog": {"all": true}, "fs": {"readFile": true, "writeFile": true, "readDir": true, "copyFile": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true}}}